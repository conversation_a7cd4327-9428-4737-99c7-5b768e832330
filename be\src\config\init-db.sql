-- Create database if not exists
CREATE DATABASE IF NOT EXISTS aitrainerhub;
USE aitrainerhub;

-- Create user table
CREATE TABLE IF NOT EXISTS user (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    token VARCHAR(255) UNIQUE NOT NULL,
    nama VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert sample user with token 'abcde'
INSERT IGNORE INTO user (token, nama) VALUES ('abcde', 'Test User');

-- Show created table
DESCRIBE user;