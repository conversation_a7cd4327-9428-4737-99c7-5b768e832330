import { NextRequest, NextResponse } from 'next/server';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
const TOKEN = process.env.NEXT_PUBLIC_TOKEN || 'abcde';

export async function POST(req: NextRequest) {
  try {
    const { threadId, message } = await req.json();

    if (!message) {
      return new NextResponse('Missing message', { status: 400 });
    }

    // Proxy request to backend with token
    const response = await fetch(`${API_URL}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TOKEN}`,
      },
      body: JSON.stringify({
        threadId,
        message,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      return new NextResponse(JSON.stringify(error), {
        status: response.status,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Chat API error:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
