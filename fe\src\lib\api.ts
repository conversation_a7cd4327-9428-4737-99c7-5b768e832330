const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
const TOKEN = process.env.NEXT_PUBLIC_TOKEN || 'abcde';

interface ApiOptions {
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    body?: any;
    headers?: Record<string, string>;
}

export async function apiCall(endpoint: string, options: ApiOptions = {}) {
    const { method = 'GET', body, headers = {} } = options;

    const fullUrl = `${API_URL}${endpoint}`;

    // Debug logging
    console.log('🔍 API Call Debug:', {
        endpoint,
        fullUrl,
        API_URL,
        TOKEN: TOKEN.substring(0, 3) + '***',
        method,
        body,
        'process.env.NEXT_PUBLIC_API_URL': process.env.NEXT_PUBLIC_API_URL,
        'process.env.NEXT_PUBLIC_TOKEN': process.env.NEXT_PUBLIC_TOKEN
    });

    const config: RequestInit = {
        method,
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${TOKEN}`,
            ...headers,
        },
    };

    if (body && method !== 'GET') {
        config.body = JSON.stringify(body);
    }

    try {
        const response = await fetch(fullUrl, config);

        console.log('📡 Response status:', response.status, response.statusText);

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error('❌ API Error:', errorData);
            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('✅ API Success:', data);
        return data;
    } catch (error) {
        console.error('💥 API call failed:', error);
        throw error;
    }
}

// Specific API functions
export const api = {
    // Health check (no token required)
    health: () => apiCall('/health'),

    // Chat endpoints (token required)
    chat: (message: string, threadId?: string) => apiCall('/api/chat', {
        method: 'POST',
        body: { message, threadId }
    }),

    getThreadMessages: (threadId: string) => apiCall(`/api/thread/${threadId}/messages`),

    // Assistant endpoints (token required)
    createThread: () => apiCall('/assistant/thread', { method: 'POST' }),

    sendMessage: (threadId: string, message: string) => apiCall('/assistant/message', {
        method: 'POST',
        body: { threadId, message }
    }),

    runAssistant: (threadId: string) => apiCall('/assistant/run', {
        method: 'POST',
        body: { threadId }
    }),

    getMessages: (threadId: string) => apiCall(`/assistant/messages/${threadId}`),
};