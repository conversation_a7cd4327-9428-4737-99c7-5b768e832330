const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
const TOKEN = process.env.NEXT_PUBLIC_TOKEN || 'abcde';

interface ApiOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  body?: any;
  headers?: Record<string, string>;
}

export async function apiCall(endpoint: string, options: ApiOptions = {}) {
  const { method = 'GET', body, headers = {} } = options;

  const config: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${TOKEN}`,
      ...headers,
    },
  };

  if (body && method !== 'GET') {
    config.body = JSON.stringify(body);
  }

  try {
    const response = await fetch(`${API_URL}${endpoint}`, config);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('API call failed:', error);
    throw error;
  }
}

// Specific API functions
export const api = {
  // Health check
  health: () => apiCall('/health'),
  
  // Chat endpoints
  chat: (message: string) => apiCall('/chat', {
    method: 'POST',
    body: { message }
  }),
  
  // Assistant endpoints
  createThread: () => apiCall('/assistant/thread', { method: 'POST' }),
  
  sendMessage: (threadId: string, message: string) => apiCall('/assistant/message', {
    method: 'POST',
    body: { threadId, message }
  }),
  
  runAssistant: (threadId: string) => apiCall('/assistant/run', {
    method: 'POST',
    body: { threadId }
  }),
  
  getMessages: (threadId: string) => apiCall(`/assistant/messages/${threadId}`),
};